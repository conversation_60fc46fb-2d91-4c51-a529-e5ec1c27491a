/**
 * @file test_command_response.cpp
 * @brief 测试命令发送和响应接收 - 发送0x1030和0x1300指令
 */

#include "../include/communication_manager.h"
#include <iostream>
#include <thread>
#include <chrono>
#include <future>
#include <atomic>
#include <mutex>
#include <iomanip>

using namespace ModernComm;

class CommandResponseTester {
private:
    std::unique_ptr<CommunicationManager> comm_manager_;
    std::atomic<bool> response_received_{false};
    std::vector<int16_t> last_response_data_;
    std::mutex response_mutex_;
    std::condition_variable response_cv_;

public:
    CommandResponseTester() {
        comm_manager_ = CommunicationFactory::create_asio_manager();
        setup_callbacks();
    }

    void setup_callbacks() {
        // 设置包接收回调 - 处理所有类型的包
        comm_manager_->set_packet_callback([this](ParsedPacket&& packet) {
            std::cout << "📥 收到数据包，类型: " << static_cast<int>(packet.type) << std::endl;
            
            if (packet.type == PacketType::Command) {
                handle_command_response(packet);
            } else if (packet.type == PacketType::PDO) {
                std::cout << "📊 收到PDO数据包 (大小: " << packet.data.size() << " 字节)" << std::endl;
            } else {
                std::cout << "📦 收到其他类型数据包 (大小: " << packet.data.size() << " 字节)" << std::endl;
            }
        });

        // 设置连接状态回调
        comm_manager_->set_connection_callback([](bool connected) {
            if (connected) {
                std::cout << "✅ 设备连接成功！" << std::endl;
            } else {
                std::cout << "❌ 设备连接断开" << std::endl;
            }
        });

        // 设置错误回调
        comm_manager_->set_error_callback([](const std::string& error_msg) {
            std::cout << "⚠️ 通讯错误: " << error_msg << std::endl;
        });
    }

    void handle_command_response(const ParsedPacket& packet) {
        std::lock_guard<std::mutex> lock(response_mutex_);
        
        std::cout << "🔍 解析命令响应数据 (" << packet.data.size() << " 字节):" << std::endl;
        
        // 打印原始字节数据
        std::cout << "原始字节: ";
        for (size_t i = 0; i < packet.data.size(); ++i) {
            std::cout << std::hex << std::setw(2) << std::setfill('0') 
                     << static_cast<int>(packet.data[i]) << " ";
        }
        std::cout << std::dec << std::endl;
        
        // 将字节数据转换为int16_t数组 (小端序)
        last_response_data_.clear();
        for (size_t i = 0; i < packet.data.size(); i += 2) {
            if (i + 1 < packet.data.size()) {
                int16_t value = static_cast<int16_t>(
                    packet.data[i] | (packet.data[i+1] << 8)
                );
                last_response_data_.push_back(value);
                std::cout << "  响应数据[" << (i/2) << "]: " << value 
                         << " (0x" << std::hex << std::setw(4) << std::setfill('0')
                         << (value & 0xFFFF) << std::dec << ")" << std::endl;
            }
        }
        
        response_received_ = true;
        response_cv_.notify_one();
    }

    bool connect_to_device(const std::string& host = "***********", int port = 6666) {
        std::cout << "🔗 正在连接到设备: " << host << ":" << port << std::endl;
        
        auto future = comm_manager_->connect_async(host, port);
        
        // 等待连接结果（10秒超时）
        auto status = future.wait_for(std::chrono::seconds(10));
        if (status == std::future_status::ready) {
            bool result = future.get();
            if (result) {
                std::cout << "🎉 连接建立成功！" << std::endl;
                return true;
            }
        }
        
        std::cout << "❌ 连接失败或超时" << std::endl;
        return false;
    }

    bool send_command_and_wait_response(const std::vector<int16_t>& command_data, 
                                       int timeout_ms = 5000) {
        std::cout << "\n📤 准备发送命令: ";
        for (size_t i = 0; i < command_data.size(); ++i) {
            std::cout << "0x" << std::hex << std::setw(4) << std::setfill('0')
                     << (command_data[i] & 0xFFFF) << std::dec;
            if (i < command_data.size() - 1) std::cout << ", ";
        }
        std::cout << std::endl;

        // 重置响应状态
        {
            std::lock_guard<std::mutex> lock(response_mutex_);
            response_received_ = false;
            last_response_data_.clear();
        }

        // 发送命令
        auto send_future = comm_manager_->send_command_async(command_data);
        
        // 等待发送完成
        auto send_status = send_future.wait_for(std::chrono::milliseconds(1000));
        if (send_status != std::future_status::ready || !send_future.get()) {
            std::cout << "❌ 命令发送失败" << std::endl;
            return false;
        }

        std::cout << "✅ 命令发送成功，等待响应..." << std::endl;

        // 等待响应（使用条件变量）
        std::unique_lock<std::mutex> lock(response_mutex_);
        bool received = response_cv_.wait_for(lock, std::chrono::milliseconds(timeout_ms), 
                                            [this] { return response_received_.load(); });

        if (!received) {
            std::cout << "⏰ 等待响应超时 (" << timeout_ms << "ms)" << std::endl;
            return false;
        }

        std::cout << "✅ 成功收到响应！" << std::endl;
        return true;
    }

    const std::vector<int16_t>& get_last_response() const {
        return last_response_data_;
    }

    void disconnect() {
        if (comm_manager_) {
            comm_manager_->disconnect();
        }
    }

    bool is_connected() const {
        return comm_manager_ ? comm_manager_->is_connected() : false;
    }
};

void print_separator(const std::string& title) {
    std::cout << "\n" << std::string(60, '=') << std::endl;
    std::cout << "  " << title << std::endl;
    std::cout << std::string(60, '=') << std::endl;
}

void test_specific_commands() {
    print_separator("ADMotion 命令响应测试");
    
    CommandResponseTester tester;
    
    // 连接设备
    if (!tester.connect_to_device()) {
        std::cout << "\n❌ 无法连接到设备，请检查：" << std::endl;
        std::cout << "   1. 设备IP地址是否正确 (***********)" << std::endl;
        std::cout << "   2. 端口号是否正确 (6666)" << std::endl;
        std::cout << "   3. 网络连接是否正常" << std::endl;
        std::cout << "   4. 设备是否已启动" << std::endl;
        return;
    }

    // 等待连接稳定
    std::cout << "⏳ 等待连接稳定..." << std::endl;
    std::this_thread::sleep_for(std::chrono::seconds(1));

    try {
        // 测试命令1: 0x1030
        print_separator("测试命令1: 0x1030");
        
        std::vector<int16_t> command1 = {0x1030};
        if (tester.send_command_and_wait_response(command1)) {
            auto response1 = tester.get_last_response();
            std::cout << "\n📋 命令1 (0x1030) 响应结果:" << std::endl;
            std::cout << "响应数据个数: " << response1.size() << std::endl;
            for (size_t i = 0; i < response1.size(); ++i) {
                std::cout << "  返回值[" << i << "]: " << response1[i] 
                         << " (0x" << std::hex << std::setw(4) << std::setfill('0')
                         << (response1[i] & 0xFFFF) << std::dec << ")" << std::endl;
            }
        } else {
            std::cout << "❌ 命令1执行失败" << std::endl;
        }

        // 等待间隔
        std::cout << "\n⏳ 等待2秒后发送下一个命令..." << std::endl;
        std::this_thread::sleep_for(std::chrono::seconds(2));

        // 测试命令2: 0x1300, 0, 0, 1
        print_separator("测试命令2: 0x1300, 0, 0, 1");
        
        std::vector<int16_t> command2 = {0x1300, 0, 0, 1};
        if (tester.send_command_and_wait_response(command2)) {
            auto response2 = tester.get_last_response();
            std::cout << "\n📋 命令2 (0x1300, 0, 0, 1) 响应结果:" << std::endl;
            std::cout << "响应数据个数: " << response2.size() << std::endl;
            for (size_t i = 0; i < response2.size(); ++i) {
                std::cout << "  返回值[" << i << "]: " << response2[i] 
                         << " (0x" << std::hex << std::setw(4) << std::setfill('0')
                         << (response2[i] & 0xFFFF) << std::dec << ")" << std::endl;
            }
        } else {
            std::cout << "❌ 命令2执行失败" << std::endl;
        }

        // 保持连接一段时间，观察是否有其他数据
        std::cout << "\n⏳ 保持连接5秒，观察其他数据..." << std::endl;
        for (int i = 0; i < 5; ++i) {
            std::this_thread::sleep_for(std::chrono::seconds(1));
            if (!tester.is_connected()) {
                std::cout << "连接已断开" << std::endl;
                break;
            }
        }

    } catch (const std::exception& e) {
        std::cout << "❌ 测试过程中发生异常: " << e.what() << std::endl;
    }

    // 断开连接
    std::cout << "\n🔌 断开连接..." << std::endl;
    tester.disconnect();
    
    print_separator("测试完成");
}

int main() {
    std::cout << "ModernCommLib - 命令响应测试程序" << std::endl;
    std::cout << "目标设备: ***********:6666" << std::endl;
    std::cout << "测试命令: 0x1030 和 0x1300,0,0,1" << std::endl;
    
    try {
        test_specific_commands();
    } catch (const std::exception& e) {
        std::cout << "❌ 程序异常: " << e.what() << std::endl;
        return 1;
    }
    
    std::cout << "\n程序结束" << std::endl;
    return 0;
}
